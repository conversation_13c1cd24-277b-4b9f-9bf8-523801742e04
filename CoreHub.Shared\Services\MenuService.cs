using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 菜单服务实现
    /// </summary>
    public class MenuService : IMenuService
    {
        private readonly DatabaseContext _dbContext;
        private readonly IUserManagementService _userManagementService;
        private readonly ILogger<MenuService> _logger;

        public MenuService(
            DatabaseContext dbContext,
            IUserManagementService userManagementService,
            ILogger<MenuService> logger)
        {
            _dbContext = dbContext;
            _userManagementService = userManagementService;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户可访问的菜单列表
        /// </summary>
        public async Task<List<MenuItem>> GetUserMenusAsync(int? userId = null)
        {
            try
            {
                _logger.LogInformation("开始获取用户菜单，用户ID：{userId}", userId);
                
                // 使用同步方法避免MultipleActiveResultSets问题
                var allMenus = _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.IsEnabled)
                    .OrderBy(m => m.SortOrder)
                    .ToList();

                _logger.LogInformation("从数据库获取到 {count} 个启用的菜单项", allMenus.Count);

                var userMenus = new List<MenuItem>();

                foreach (var menu in allMenus)
                {
                    _logger.LogDebug("处理菜单：{menuCode} - {menuName}, IsPublic: {isPublic}, PermissionCode: {permissionCode}", 
                        menu.Code, menu.Name, menu.IsPublic, menu.PermissionCode);

                    // 公开菜单（如首页）对所有用户可见
                    if (menu.IsPublic)
                    {
                        userMenus.Add(menu);
                        _logger.LogDebug("添加公开菜单：{menuName}", menu.Name);
                        continue;
                    }

                    // 未登录用户只能看到公开菜单
                    if (userId == null)
                    {
                        _logger.LogDebug("未登录用户跳过非公开菜单：{menuName}", menu.Name);
                        continue;
                    }

                    // 检查用户是否有权限访问此菜单
                    if (!string.IsNullOrEmpty(menu.PermissionCode))
                    {
                        // 检查是否为超级管理员
                        var user = _dbContext.Db.Queryable<User>().Where(u => u.Id == userId.Value).First();
                        if (user != null && user.Username.ToLower() == "admin")
                        {
                            // 超级管理员可以访问所有菜单
                            userMenus.Add(menu);
                            _logger.LogDebug("admin用户可以访问所有菜单，添加：{menuName}", menu.Name);
                        }
                        else
                        {
                            var hasPermission = await _userManagementService.CheckUserPermissionAsync(userId.Value, menu.PermissionCode);
                            if (hasPermission)
                            {
                                userMenus.Add(menu);
                                _logger.LogDebug("用户有权限，添加菜单：{menuName}", menu.Name);
                            }
                            else
                            {
                                _logger.LogDebug("用户无权限，跳过菜单：{menuName} (权限: {permissionCode})", menu.Name, menu.PermissionCode);
                            }
                        }
                    }
                    else
                    {
                        // 没有权限要求的菜单，登录用户都可以看到
                        userMenus.Add(menu);
                        _logger.LogDebug("无权限要求的菜单，添加：{menuName}", menu.Name);
                    }
                }

                _logger.LogInformation("用户 {userId} 最终获得 {count} 个菜单", userId, userMenus.Count);

                // 构建菜单树结构
                var result = BuildMenuTree(userMenus);
                _logger.LogInformation("构建菜单树后返回 {count} 个根菜单项", result.Count);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户菜单失败：{userId}", userId);
                return new List<MenuItem>();
            }
        }

        /// <summary>
        /// 获取所有菜单（管理用）
        /// </summary>
        public async Task<List<MenuItem>> GetAllMenusAsync()
        {
            try
            {
                // 使用同步方法避免MultipleActiveResultSets问题
                var allMenus = _dbContext.Db.Queryable<MenuItem>()
                    .OrderBy(m => m.SortOrder)
                    .ToList();

                return BuildMenuTree(allMenus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有菜单失败");
                return new List<MenuItem>();
            }
        }

        /// <summary>
        /// 创建菜单
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> CreateMenuAsync(MenuItem menuItem)
        {
            try
            {
                // 检查菜单编码是否已存在
                var existingMenu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Code == menuItem.Code)
                    .FirstAsync();

                if (existingMenu != null)
                    return (false, "菜单编码已存在");

                menuItem.CreatedAt = DateTime.Now;

                await _dbContext.Db.Insertable(menuItem).ExecuteCommandAsync();

                _logger.LogInformation("菜单创建成功：{menuName}", menuItem.Name);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建菜单失败：{menuName}", menuItem.Name);
                return (false, $"创建菜单失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新菜单
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateMenuAsync(MenuItem menuItem)
        {
            try
            {
                // 检查菜单是否存在
                var existingMenu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Id == menuItem.Id)
                    .FirstAsync();

                if (existingMenu == null)
                    return (false, "菜单不存在");

                // 检查菜单编码是否已被其他菜单使用
                if (existingMenu.Code != menuItem.Code)
                {
                    var duplicateMenu = await _dbContext.Db.Queryable<MenuItem>()
                        .Where(m => m.Code == menuItem.Code && m.Id != menuItem.Id)
                        .FirstAsync();

                    if (duplicateMenu != null)
                        return (false, "菜单编码已被其他菜单使用");
                }

                menuItem.UpdatedAt = DateTime.Now;

                await _dbContext.Db.Updateable(menuItem)
                    .IgnoreColumns(m => new { m.CreatedAt, m.CreatedBy })
                    .ExecuteCommandAsync();

                _logger.LogInformation("菜单更新成功：{menuId}", menuItem.Id);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新菜单失败：{menuId}", menuItem.Id);
                return (false, $"更新菜单失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除菜单
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteMenuAsync(int menuId)
        {
            try
            {
                var menu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Id == menuId)
                    .FirstAsync();

                if (menu == null)
                    return (false, "菜单不存在");

                if (menu.IsSystem)
                    return (false, "系统菜单不能删除");

                // 检查是否有子菜单
                var childCount = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.ParentId == menuId)
                    .CountAsync();

                if (childCount > 0)
                    return (false, "该菜单下还有子菜单，无法删除");

                await _dbContext.Db.Deleteable<MenuItem>()
                    .Where(m => m.Id == menuId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("菜单删除成功：{menuId}", menuId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除菜单失败：{menuId}", menuId);
                return (false, $"删除菜单失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 初始化默认菜单数据
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> InitializeDefaultMenusAsync()
        {
            try
            {
                // 检查是否已有菜单数据
                var menuCount = await _dbContext.Db.Queryable<MenuItem>().CountAsync();
                if (menuCount > 0)
                    return (true, "菜单数据已存在");

                var defaultMenus = new List<MenuItem>
                {
                    // 首页 - 公开菜单
                    new MenuItem
                    {
                        Code = "Home",
                        Name = "首页",
                        Description = "系统首页",
                        RouteUrl = "",
                        Icon = "bi bi-house-door-fill-nav-menu",
                        Level = 1,
                        SortOrder = 1,
                        IsPublic = true,
                        IsSystem = true,
                        MenuType = 1
                    },

                    // 计数器
                    new MenuItem
                    {
                        Code = "Counter",
                        Name = "计数器",
                        Description = "计数器页面",
                        RouteUrl = "counter",
                        Icon = "bi bi-plus-square-fill-nav-menu",
                        Level = 1,
                        SortOrder = 10,
                        PermissionCode = "Counter.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    // 设备扫描
                    new MenuItem
                    {
                        Code = "DeviceScanner",
                        Name = "设备扫描",
                        Description = "设备扫描页面",
                        RouteUrl = "devicescanner",
                        Icon = "bi bi-camera-fill-nav-menu",
                        Level = 1,
                        SortOrder = 20,
                        PermissionCode = "DeviceScanner.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    // 摄像头测试
                    new MenuItem
                    {
                        Code = "CameraTest",
                        Name = "摄像头测试",
                        Description = "摄像头测试页面",
                        RouteUrl = "camera-test",
                        Icon = "bi bi-camera2-nav-menu",
                        Level = 1,
                        SortOrder = 30,
                        PermissionCode = "CameraTest.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    // 天气预报
                    new MenuItem
                    {
                        Code = "Weather",
                        Name = "天气预报",
                        Description = "天气预报页面",
                        RouteUrl = "weather",
                        Icon = "bi bi-list-nested-nav-menu",
                        Level = 1,
                        SortOrder = 40,
                        PermissionCode = "Weather.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    // 设备报修
                    new MenuItem
                    {
                        Code = "DeviceRepair",
                        Name = "设备报修",
                        Description = "设备报修页面",
                        RouteUrl = "devicerepair",
                        Icon = "bi bi-tools-nav-menu",
                        Level = 1,
                        SortOrder = 50,
                        PermissionCode = "DeviceRepair.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    // 设备管理分组
                    new MenuItem
                    {
                        Code = "EquipmentManagement",
                        Name = "设备管理",
                        Description = "设备管理分组",
                        Icon = "bi bi-gear-nav-menu",
                        Level = 1,
                        SortOrder = 50,
                        IsSystem = true,
                        MenuType = 2 // 分组标题
                    },

                    // 系统管理分组
                    new MenuItem
                    {
                        Code = "SystemManagement",
                        Name = "系统管理",
                        Description = "系统管理分组",
                        Icon = "bi bi-gear-nav-menu",
                        Level = 1,
                        SortOrder = 100,
                        IsSystem = true,
                        MenuType = 2 // 分组标题
                    }
                };

                // 插入一级菜单
                await _dbContext.Db.Insertable(defaultMenus).ExecuteCommandAsync();

                // 获取设备管理分组的ID
                var equipmentManagementMenu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Code == "EquipmentManagement")
                    .FirstAsync();

                // 获取系统管理分组的ID
                var systemManagementMenu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Code == "SystemManagement")
                    .FirstAsync();

                // 设备管理子菜单
                var equipmentSubMenus = new List<MenuItem>
                {
                    new MenuItem
                    {
                        Code = "DepartmentManagement",
                        Name = "部门管理",
                        Description = "部门信息管理",
                        RouteUrl = "department-management",
                        Icon = "bi bi-building-nav-menu",
                        ParentId = equipmentManagementMenu.Id,
                        Level = 2,
                        SortOrder = 1,
                        PermissionCode = "Department.Manage",
                        IsSystem = true,
                        MenuType = 1
                    },
                    new MenuItem
                    {
                        Code = "EquipmentModelManagement",
                        Name = "设备型号管理",
                        Description = "设备型号信息管理",
                        RouteUrl = "equipment-model-management",
                        Icon = "bi bi-cpu-nav-menu",
                        ParentId = equipmentManagementMenu.Id,
                        Level = 2,
                        SortOrder = 2,
                        PermissionCode = "EquipmentModel.Manage",
                        IsSystem = true,
                        MenuType = 1
                    },
                    new MenuItem
                    {
                        Code = "LocationManagement",
                        Name = "位置管理",
                        Description = "设备位置信息管理",
                        RouteUrl = "location-management",
                        Icon = "bi bi-geo-alt-nav-menu",
                        ParentId = equipmentManagementMenu.Id,
                        Level = 2,
                        SortOrder = 3,
                        PermissionCode = "Location.Manage",
                        IsSystem = true,
                        MenuType = 1
                    },
                    new MenuItem
                    {
                        Code = "EquipmentManagementPage",
                        Name = "设备管理",
                        Description = "设备信息管理",
                        RouteUrl = "equipment-management",
                        Icon = "bi bi-pc-display-nav-menu",
                        ParentId = equipmentManagementMenu.Id,
                        Level = 2,
                        SortOrder = 4,
                        PermissionCode = "Equipment.Manage",
                        IsSystem = true,
                        MenuType = 1
                    },
                    new MenuItem
                    {
                        Code = "CreateRepairOrder",
                        Name = "设备报修",
                        Description = "提交设备报修申请",
                        RouteUrl = "create-repair-order",
                        Icon = "bi bi-tools-nav-menu",
                        ParentId = equipmentManagementMenu.Id,
                        Level = 2,
                        SortOrder = 5,
                        PermissionCode = "RepairOrder.Create",
                        IsSystem = true,
                        MenuType = 1
                    },
                    new MenuItem
                    {
                        Code = "RepairOrderManagement",
                        Name = "报修单管理",
                        Description = "报修单信息管理",
                        RouteUrl = "repair-order-management",
                        Icon = "bi bi-clipboard-check-nav-menu",
                        ParentId = equipmentManagementMenu.Id,
                        Level = 2,
                        SortOrder = 6,
                        PermissionCode = "RepairOrder.Manage",
                        IsSystem = true,
                        MenuType = 1
                    }
                };

                // 系统管理子菜单
                var systemSubMenus = new List<MenuItem>
                {
                    new MenuItem
                    {
                        Code = "UserManagement",
                        Name = "用户管理",
                        Description = "用户管理页面",
                        RouteUrl = "users",
                        Icon = "bi bi-people-nav-menu",
                        ParentId = systemManagementMenu.Id,
                        Level = 2,
                        SortOrder = 101,
                        PermissionCode = "UserManagement.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    new MenuItem
                    {
                        Code = "RoleManagement",
                        Name = "角色管理",
                        Description = "角色管理页面",
                        RouteUrl = "roles",
                        Icon = "bi bi-person-badge-nav-menu",
                        ParentId = systemManagementMenu.Id,
                        Level = 2,
                        SortOrder = 102,
                        PermissionCode = "RoleManagement.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    new MenuItem
                    {
                        Code = "PermissionManagement",
                        Name = "权限管理",
                        Description = "权限管理页面",
                        RouteUrl = "permissions",
                        Icon = "bi bi-key-nav-menu",
                        ParentId = systemManagementMenu.Id,
                        Level = 2,
                        SortOrder = 103,
                        PermissionCode = "PermissionManagement.View",
                        IsSystem = true,
                        MenuType = 1
                    },

                    new MenuItem
                    {
                        Code = "UserPermissions",
                        Name = "权限分配",
                        Description = "权限分配页面",
                        RouteUrl = "user-permissions",
                        Icon = "bi bi-person-gear-nav-menu",
                        ParentId = systemManagementMenu.Id,
                        Level = 2,
                        SortOrder = 104,
                        PermissionCode = "UserManagement.AssignRoles",
                        IsSystem = true,
                        MenuType = 1
                    },

                    new MenuItem
                    {
                        Code = "UserDepartmentAssignment",
                        Name = "用户所属部门",
                        Description = "用户所属部门管理页面，设置用户在组织架构中的归属",
                        RouteUrl = "user-department-assignment-management",
                        Icon = "bi bi-person-workspace-nav-menu",
                        ParentId = systemManagementMenu.Id,
                        Level = 2,
                        SortOrder = 105,
                        PermissionCode = "UserManagement.AssignDepartment",
                        IsSystem = true,
                        MenuType = 1
                    }
                };

                // 插入设备管理子菜单
                await _dbContext.Db.Insertable(equipmentSubMenus).ExecuteCommandAsync();

                // 插入系统管理子菜单
                await _dbContext.Db.Insertable(systemSubMenus).ExecuteCommandAsync();

                // 其他菜单
                var otherMenus = new List<MenuItem>
                {
                    new MenuItem
                    {
                        Code = "AuthorizeViewExample",
                        Name = "权限控制示例",
                        Description = "权限控制示例页面",
                        RouteUrl = "authorize-view-example",
                        Icon = "bi bi-shield-check-nav-menu",
                        Level = 1,
                        SortOrder = 200,
                        IsSystem = true,
                        MenuType = 1
                    },

                    new MenuItem
                    {
                        Code = "DebugAuth",
                        Name = "认证调试",
                        Description = "认证调试页面",
                        RouteUrl = "debug-auth",
                        Icon = "bi bi-bug-nav-menu",
                        Level = 1,
                        SortOrder = 210,
                        IsSystem = true,
                        MenuType = 1
                    }
                };

                await _dbContext.Db.Insertable(otherMenus).ExecuteCommandAsync();

                _logger.LogInformation("默认菜单数据初始化成功");
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化默认菜单数据失败");
                return (false, $"初始化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 构建菜单树结构
        /// </summary>
        private List<MenuItem> BuildMenuTree(List<MenuItem> allMenus)
        {
            var menuDict = allMenus.ToDictionary(m => m.Id, m => m);
            var rootMenus = new List<MenuItem>();

            foreach (var menu in allMenus)
            {
                if (menu.ParentId == null)
                {
                    rootMenus.Add(menu);
                }
                else if (menuDict.ContainsKey(menu.ParentId.Value))
                {
                    var parent = menuDict[menu.ParentId.Value];
                    parent.Children.Add(menu);
                    menu.Parent = parent;
                }
            }

            return rootMenus.OrderBy(m => m.SortOrder).ToList();
        }
    }
} 