﻿@using CoreHub.Shared.Services
@using CoreHub.Shared.Components
@using System.Security.Claims
@using DatabaseMenuItem = CoreHub.Shared.Models.Database.MenuItem
@implements IDisposable

@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider
@inject IMenuService MenuService


    <!-- 侧边栏抽屉内容 -->
    <MudNavMenu Color="Color.Primary" Bordered="true">
        <AuthorizeView>
            <Authorized>
                <!-- 所有已登录用户使用动态菜单 -->
                @{
                    var userName = context?.User?.Identity?.Name?.ToLower();
                    var userMenuCount = userMenus?.Count ?? 0;
                }
                
                @if (userMenus != null && userMenus.Any())
                {
                    @foreach (var menu in userMenus.Where(m => m.IsEnabled).OrderBy(m => m.SortOrder))
                    {
                        @RenderMudMenuItem(menu)
                    }
                }
                else
                {
                    <!-- 如果没有菜单数据，显示提示信息 -->
                    <MudAlert Severity="Severity.Info" Dense="true">
                        正在加载菜单...
                    </MudAlert>
                }
                
                <!-- 调试菜单（仅在开发环境显示） -->
                <MudDivider Class="my-2" />

                <MudNavLink Href="/fix-maintenance-permission" Icon="@Icons.Material.Filled.Build">修复维修权限</MudNavLink>
                <MudNavLink Href="/test-maintenance-departments" Icon="@Icons.Material.Filled.BugReport">测试维修部门</MudNavLink>
                <MudNavLink Href="/workflow-history-test" Icon="@Icons.Material.Filled.History">工作流历史测试</MudNavLink>
                @* <MudNavLink Href="/menu-debug" Icon="@Icons.Material.Filled.BugReport">菜单调试</MudNavLink> *@

            </Authorized>
            <NotAuthorized>
                <!-- 未登录用户菜单 -->
                @if (publicMenus != null && publicMenus.Any())
                {
                    @foreach (var menu in publicMenus.Where(m => m.IsEnabled).OrderBy(m => m.SortOrder))
                    {
                        @RenderMudMenuItem(menu)
                    }
                }
                else
                {
                    <!-- 如果没有公开菜单，显示默认菜单 -->
                    <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">首页</MudNavLink>
                }
                
                <MudDivider Class="my-2" />
                <MudNavLink Href="/login" Icon="@Icons.Material.Filled.Login">登录</MudNavLink>
                @* <MudNavLink Href="/menu-debug" Icon="@Icons.Material.Filled.BugReport">菜单调试</MudNavLink>
                <MudNavLink Href="/menu-quick-fix" Icon="@Icons.Material.Filled.Build">菜单修复</MudNavLink> *@
            </NotAuthorized>
        </AuthorizeView>
    </MudNavMenu>

@code {
    
    private List<DatabaseMenuItem>? userMenus;
    private List<DatabaseMenuItem>? publicMenus;
    private int? currentUserId;

    protected override async Task OnInitializedAsync()
    {
        await LoadMenusAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // 监听认证状态变化
            AuthStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
        }
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> task)
    {
        try
        {
            await LoadMenusAsync();
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"认证状态变化处理异常: {ex}");
        }
    }

    private async Task LoadMenusAsync()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                // 获取当前用户ID
                var userIdClaim = user.FindFirst("UserId")?.Value;
                if (int.TryParse(userIdClaim, out var userId))
                {
                    currentUserId = userId;
                    userMenus = await MenuService.GetUserMenusAsync(userId);
                }
                else
                {
                    currentUserId = null;
                    userMenus = await MenuService.GetUserMenusAsync();
                }
            }
            else
            {
                currentUserId = null;
                publicMenus = await MenuService.GetUserMenusAsync(); // 获取公开菜单
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载菜单失败: {ex}");
        }
    }

    private RenderFragment RenderMudMenuItem(DatabaseMenuItem menu) => builder =>
    {
        try
        {
            if (menu == null || !menu.IsEnabled) return;
            
            if (menu.MenuType == 2) // 分组标题
            {
                // 检查是否有可显示的子菜单
                var enabledChildren = menu.Children?.Where(c => c.IsEnabled).ToList();
                if (enabledChildren != null && enabledChildren.Any())
                {
                    builder.OpenComponent<MudNavGroup>(0);
                    builder.AddAttribute(1, "Title", menu.Name);
                    builder.AddAttribute(2, "Icon", GetMudIcon(menu.Icon));
                    builder.AddAttribute(3, "Expanded", true);
                    
                    builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                    {
                        foreach (var child in enabledChildren.OrderBy(c => c.SortOrder))
                        {
                            childBuilder.AddContent(0, RenderMudMenuItem(child));
                        }
                    }));
                    
                    builder.CloseComponent();
                }
            }
            else // 普通菜单项
            {
                builder.OpenComponent<MudNavLink>(0);
                builder.AddAttribute(1, "Href", menu.RouteUrl ?? "");
                builder.AddAttribute(2, "Icon", GetMudIcon(menu.Icon));
                
                if (menu.RouteUrl == "" || menu.RouteUrl == "/")
                {
                    builder.AddAttribute(3, "Match", NavLinkMatch.All);
                }
                
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, menu.Name);
                }));
                
                builder.CloseComponent();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"RenderMudMenuItem 异常: {ex}");
            // 渲染错误信息
            builder.OpenComponent<MudAlert>(0);
            builder.AddAttribute(1, "Severity", Severity.Error);
            builder.AddAttribute(2, "Dense", true);
            builder.AddAttribute(3, "ChildContent", (RenderFragment)(childBuilder =>
            {
                childBuilder.AddContent(0, $"菜单渲染错误: {menu?.Name ?? "未知菜单"}");
            }));
            builder.CloseComponent();
        }
    };

    private string GetMudIcon(string? iconPath)
    {
        if (string.IsNullOrEmpty(iconPath))
            return Icons.Material.Filled.Circle;
            
        // 如果是MudBlazor图标名称常量，需要转换为实际值
        if (iconPath.StartsWith("Icons.Material."))
        {
            return ConvertIconConstantToValue(iconPath);
        }
        
        // 兼容旧格式的图标映射（向后兼容）
        return iconPath switch
        {
            // 简化图标名称映射
            "home" => Icons.Material.Filled.Home,
            "add" => Icons.Material.Filled.Add,
            "qr_code_scanner" => Icons.Material.Filled.QrCodeScanner,
            "camera_alt" => Icons.Material.Filled.Camera,
            "wb_sunny" => Icons.Material.Filled.WbSunny,
            "build" => Icons.Material.Filled.Build,
            "settings" => Icons.Material.Filled.Settings,
            "people" => Icons.Material.Filled.People,
            "security" => Icons.Material.Filled.Security,
            "key" => Icons.Material.Filled.Key,
            "menu" => Icons.Material.Filled.Menu,
            "person_add" => Icons.Material.Filled.PersonAdd,
            "storage" => Icons.Material.Filled.Storage,
            "shield" => Icons.Material.Filled.Shield,
            "bug_report" => Icons.Material.Filled.BugReport,
            "login" => Icons.Material.Filled.Login,
            "code" => Icons.Material.Filled.Code,
            "palette" => Icons.Material.Filled.Palette,
            "account_tree" => Icons.Material.Filled.AccountTree,
            "category" => Icons.Material.Filled.Category,
            "work" => Icons.Material.Filled.Work,
            
            // Bootstrap图标兼容（保持向后兼容性）
            "bi bi-house-door-fill-nav-menu" or "bi bi-house-door-fill" => Icons.Material.Filled.Home,
            "bi bi-plus-square-fill-nav-menu" or "bi bi-plus-square-fill" => Icons.Material.Filled.Add,
            "bi bi-list-nested-nav-menu" or "bi bi-list-nested" => Icons.Material.Filled.List,
            "bi bi-camera-fill-nav-menu" or "bi bi-camera-fill" => Icons.Material.Filled.QrCodeScanner,
            "bi bi-camera2-nav-menu" => Icons.Material.Filled.Camera,
            "bi bi-tools-nav-menu" or "bi bi-tools" => Icons.Material.Filled.Build,
            "bi bi-gear-nav-menu" or "bi bi-gear" => Icons.Material.Filled.Settings,
            "bi bi-people-nav-menu" or "bi bi-people" => Icons.Material.Filled.People,
            "bi bi-person-badge-nav-menu" or "bi bi-person-badge" => Icons.Material.Filled.Security,
            "bi bi-key-nav-menu" or "bi bi-key" => Icons.Material.Filled.Key,
            "bi bi-list-ul" => Icons.Material.Filled.Menu,
            "bi bi-person-gear-nav-menu" or "bi bi-person-gear" => Icons.Material.Filled.PersonAdd,
            "bi bi-database-gear" => Icons.Material.Filled.Storage,
            "bi bi-shield-check-nav-menu" => Icons.Material.Filled.Shield,
            "bi bi-bug-nav-menu" or "bi bi-bug" => Icons.Material.Filled.BugReport,
            "bi bi-box-arrow-in-right-nav-menu" => Icons.Material.Filled.Login,
            "bi bi-code-slash" => Icons.Material.Filled.Code,
            "bi bi-palette" => Icons.Material.Filled.Palette,
            "bi bi-diagram-2" => Icons.Material.Filled.AccountTree,
            "bi bi-wrench" => Icons.Material.Filled.Build,
            
            _ => Icons.Material.Filled.Circle
        };
    }

    private string ConvertIconConstantToValue(string iconConstant)
    {
        return iconConstant switch
        {
            // 常用图标
            "Icons.Material.Filled.Home" => Icons.Material.Filled.Home,
            "Icons.Material.Filled.Add" => Icons.Material.Filled.Add,
            "Icons.Material.Filled.Settings" => Icons.Material.Filled.Settings,
            "Icons.Material.Filled.Search" => Icons.Material.Filled.Search,
            "Icons.Material.Filled.Delete" => Icons.Material.Filled.Delete,
            "Icons.Material.Filled.Edit" => Icons.Material.Filled.Edit,
            "Icons.Material.Filled.Refresh" => Icons.Material.Filled.Refresh,
            "Icons.Material.Filled.Close" => Icons.Material.Filled.Close,
            "Icons.Material.Filled.Check" => Icons.Material.Filled.Check,
            "Icons.Material.Filled.Clear" => Icons.Material.Filled.Clear,
            "Icons.Material.Filled.Info" => Icons.Material.Filled.Info,
            "Icons.Material.Filled.Warning" => Icons.Material.Filled.Warning,
            
            // 导航类
            "Icons.Material.Filled.Menu" => Icons.Material.Filled.Menu,
            "Icons.Material.Filled.ArrowBack" => Icons.Material.Filled.ArrowBack,
            "Icons.Material.Filled.ExpandMore" => Icons.Material.Filled.ExpandMore,
            "Icons.Material.Filled.ExpandLess" => Icons.Material.Filled.ExpandLess,
            "Icons.Material.Filled.ChevronLeft" => Icons.Material.Filled.ChevronLeft,
            "Icons.Material.Filled.ChevronRight" => Icons.Material.Filled.ChevronRight,
            "Icons.Material.Filled.Dashboard" => Icons.Material.Filled.Dashboard,
            "Icons.Material.Filled.List" => Icons.Material.Filled.List,
            "Icons.Material.Filled.AccountTree" => Icons.Material.Filled.AccountTree,
            
            // 系统管理
            "Icons.Material.Filled.People" => Icons.Material.Filled.People,
            "Icons.Material.Filled.Person" => Icons.Material.Filled.Person,
            "Icons.Material.Filled.PersonAdd" => Icons.Material.Filled.PersonAdd,
            "Icons.Material.Filled.Group" => Icons.Material.Filled.Group,
            "Icons.Material.Filled.Security" => Icons.Material.Filled.Security,
            "Icons.Material.Filled.Lock" => Icons.Material.Filled.Lock,
            "Icons.Material.Filled.LockOpen" => Icons.Material.Filled.LockOpen,
            "Icons.Material.Filled.Key" => Icons.Material.Filled.Key,
            "Icons.Material.Filled.Shield" => Icons.Material.Filled.Shield,
            "Icons.Material.Filled.AdminPanelSettings" => Icons.Material.Filled.AdminPanelSettings,
            "Icons.Material.Filled.PeopleAlt" => Icons.Material.Filled.PeopleAlt,
            
            // 操作类
            "Icons.Material.Filled.Build" => Icons.Material.Filled.Build,
            "Icons.Material.Filled.Send" => Icons.Material.Filled.Send,
            "Icons.Material.Filled.Save" => Icons.Material.Filled.Save,
            "Icons.Material.Filled.Download" => Icons.Material.Filled.Download,
            "Icons.Material.Filled.Upload" => Icons.Material.Filled.Upload,
            "Icons.Material.Filled.FileCopy" => Icons.Material.Filled.FileCopy,
            "Icons.Material.Filled.Share" => Icons.Material.Filled.Share,
            "Icons.Material.Filled.Print" => Icons.Material.Filled.Print,
            "Icons.Material.Filled.Visibility" => Icons.Material.Filled.Visibility,
            "Icons.Material.Filled.VisibilityOff" => Icons.Material.Filled.VisibilityOff,
            "Icons.Material.Filled.Login" => Icons.Material.Filled.Login,
            "Icons.Material.Filled.Logout" => Icons.Material.Filled.Logout,
            "Icons.Material.Filled.AutoFixHigh" => Icons.Material.Filled.AutoFixHigh,
            "Icons.Material.Filled.ClearAll" => Icons.Material.Filled.ClearAll,
            
            // 内容类
            "Icons.Material.Filled.Folder" => Icons.Material.Filled.Folder,
            "Icons.Material.Filled.FolderOpen" => Icons.Material.Filled.FolderOpen,
            "Icons.Material.Filled.Description" => Icons.Material.Filled.Description,
            "Icons.Material.Filled.Article" => Icons.Material.Filled.Article,
            "Icons.Material.Filled.Note" => Icons.Material.Filled.Note,
            "Icons.Material.Filled.Image" => Icons.Material.Filled.Image,
            "Icons.Material.Filled.VideoLibrary" => Icons.Material.Filled.VideoLibrary,
            "Icons.Material.Filled.AudioFile" => Icons.Material.Filled.AudioFile,
            "Icons.Material.Filled.GridView" => Icons.Material.Filled.GridView,
            "Icons.Material.Filled.TableChart" => Icons.Material.Filled.TableChart,
            "Icons.Material.Filled.Label" => Icons.Material.Filled.Label,
            "Icons.Material.Filled.LocalOffer" => Icons.Material.Filled.LocalOffer,
            "Icons.Material.Filled.Category" => Icons.Material.Filled.Category,
            "Icons.Material.Filled.Flag" => Icons.Material.Filled.Flag,
            
            // 设备/技术类
            "Icons.Material.Filled.QrCodeScanner" => Icons.Material.Filled.QrCodeScanner,
            "Icons.Material.Filled.CameraAlt" => Icons.Material.Filled.CameraAlt,
            "Icons.Material.Filled.Camera" => Icons.Material.Filled.CameraAlt, // 兼容性
            "Icons.Material.Filled.Phone" => Icons.Material.Filled.Phone,
            "Icons.Material.Filled.Computer" => Icons.Material.Filled.Computer,
            "Icons.Material.Filled.Storage" => Icons.Material.Filled.Storage,
            "Icons.Material.Filled.Cloud" => Icons.Material.Filled.Cloud,
            "Icons.Material.Filled.Wifi" => Icons.Material.Filled.Wifi,
            "Icons.Material.Filled.Bluetooth" => Icons.Material.Filled.Bluetooth,
            "Icons.Material.Filled.Api" => Icons.Material.Filled.Api,
            "Icons.Material.Filled.Web" => Icons.Material.Filled.Web,
            "Icons.Material.Filled.WebAsset" => Icons.Material.Filled.WebAsset,
            "Icons.Material.Filled.Architecture" => Icons.Material.Filled.Architecture,
            "Icons.Material.Filled.Science" => Icons.Material.Filled.Science,
            "Icons.Material.Filled.Keyboard" => Icons.Material.Filled.Keyboard,
            
            // 状态指示
            "Icons.Material.Filled.CheckCircle" => Icons.Material.Filled.CheckCircle,
            "Icons.Material.Filled.Error" => Icons.Material.Filled.Error,
            "Icons.Material.Filled.Cancel" => Icons.Material.Filled.Cancel,
            "Icons.Material.Filled.Help" => Icons.Material.Filled.Help,
            "Icons.Material.Filled.Notifications" => Icons.Material.Filled.Notifications,
            "Icons.Material.Filled.NotificationsOff" => Icons.Material.Filled.NotificationsOff,
            
            // 权限和人员管理
            "Icons.Material.Filled.Engineering" => Icons.Material.Filled.Engineering,

            // 开发/调试
            "Icons.Material.Filled.Code" => Icons.Material.Filled.Code,
            "Icons.Material.Filled.BugReport" => Icons.Material.Filled.BugReport,
            "Icons.Material.Filled.Terminal" => Icons.Material.Filled.Terminal,
            
            // 时间/历史
            "Icons.Material.Filled.History" => Icons.Material.Filled.History,
            "Icons.Material.Filled.Schedule" => Icons.Material.Filled.Schedule,
            "Icons.Material.Filled.AccessTime" => Icons.Material.Filled.AccessTime,
            "Icons.Material.Filled.Today" => Icons.Material.Filled.Today,
            "Icons.Material.Filled.Event" => Icons.Material.Filled.Event,
            
            // 其他常用
            "Icons.Material.Filled.Star" => Icons.Material.Filled.Star,
            "Icons.Material.Filled.Favorite" => Icons.Material.Filled.Favorite,
            "Icons.Material.Filled.Bookmark" => Icons.Material.Filled.Bookmark,
            
            // 颜色/样式
            "Icons.Material.Filled.Palette" => Icons.Material.Filled.Palette,
            "Icons.Material.Filled.Brush" => Icons.Material.Filled.Brush,
            "Icons.Material.Filled.ColorLens" => Icons.Material.Filled.ColorLens,
            "Icons.Material.Filled.FormatPaint" => Icons.Material.Filled.FormatPaint,
            
            // 天气
            "Icons.Material.Filled.WbSunny" => Icons.Material.Filled.WbSunny,
            "Icons.Material.Filled.BeachAccess" => Icons.Material.Filled.BeachAccess,
            
            _ => Icons.Material.Filled.Circle // 默认图标
        };
    }

    public void Dispose()
    {
        AuthStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
}

