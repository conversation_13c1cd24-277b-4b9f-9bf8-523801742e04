# 基于角色的设备报修权限控制系统 - 完整部署和测试指南

## 系统概述

本系统实现了一个完整的基于角色的权限控制（RBAC）机制，用于管理设备报修流程。系统包含以下核心功能：

### 🎯 核心功能
1. **基于角色的部门权限控制**：用户根据角色获得不同部门的操作权限
2. **智能维修人员管理**：根据技能和工作负载智能分配维修任务
3. **设备型号技能匹配**：确保维修人员具备所需技能
4. **多层权限验证**：前端过滤 + 后端验证 + 数据库约束
5. **权限管理界面**：可视化管理角色权限和维修人员

### 🏗️ 系统架构
- **前端**：Blazor组件，支持MAUI和Web
- **后端**：.NET服务层，SqlSugar ORM
- **数据库**：SQL Server，完整的权限控制表结构
- **权限模型**：RBAC + 部门级权限控制

## 部署步骤

### 1. 数据库部署

#### 1.1 执行数据库脚本
```sql
-- 执行更新后的数据库脚本
-- 文件：数据库脚本_完整版.sql
```

该脚本将创建：
- 角色部门权限表（RoleDepartmentPermissions）
- 维修人员表（MaintenancePersonnel）
- 设备型号技能要求表（EquipmentModelSkills）
- 相关索引和约束
- 示例数据和权限配置

#### 1.2 验证数据库结构
```sql
-- 验证新表是否创建成功
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME IN ('RoleDepartmentPermissions', 'MaintenancePersonnel', 'EquipmentModelSkills')

-- 验证权限数据是否插入
SELECT COUNT(*) FROM RoleDepartmentPermissions
SELECT COUNT(*) FROM MaintenancePersonnel
SELECT COUNT(*) FROM EquipmentModelSkills
```

### 2. 代码部署

#### 2.1 新增文件清单
确保以下文件已正确添加到项目中：

**模型文件：**
- `MauiApp5.Shared/Models/Database/RoleDepartmentPermission.cs`
- `MauiApp5.Shared/Models/Database/MaintenancePersonnel.cs`
- `MauiApp5.Shared/Models/Database/EquipmentModelSkill.cs`

**服务文件：**
- `MauiApp5.Shared/Services/IRoleDepartmentPermissionService.cs`
- `MauiApp5.Shared/Services/IMaintenancePersonnelService.cs`
- `MauiApp5.Shared/Services/MaintenancePersonnelService.cs`
- `MauiApp5.Shared/Services/IPermissionValidationService.cs`
- `MauiApp5.Shared/Services/PermissionValidationService.cs`

**页面文件：**
- `MauiApp5.Shared/Pages/RoleDepartmentPermissionManagement.razor`
- `MauiApp5.Shared/Pages/MaintenancePersonnelManagement.razor`
- `MauiApp5.Shared/Pages/TestUserDepartment.razor`（已更新）
- `MauiApp5.Shared/Pages/CreateRepairOrder.razor`（已更新）

#### 2.2 服务注册验证
确认以下服务已在两个项目中注册：

**MauiApp5/MauiProgram.cs：**
```csharp
builder.Services.AddScoped<IRoleDepartmentPermissionService, RoleDepartmentPermissionService>();
builder.Services.AddScoped<IMaintenancePersonnelService, MaintenancePersonnelService>();
builder.Services.AddScoped<IPermissionValidationService, PermissionValidationService>();
```

**MauiApp5.Web/Program.cs：**
```csharp
builder.Services.AddScoped<IRoleDepartmentPermissionService, RoleDepartmentPermissionService>();
builder.Services.AddScoped<IMaintenancePersonnelService, MaintenancePersonnelService>();
builder.Services.AddScoped<IPermissionValidationService, PermissionValidationService>();
```

### 3. 编译和启动

#### 3.1 编译检查
```bash
# 清理和重新编译
dotnet clean
dotnet build

# 检查编译错误
# 确保所有新增的服务和模型都能正确编译
```

#### 3.2 启动应用
```bash
# 启动Web应用
cd MauiApp5.Web
dotnet run

# 或启动MAUI应用
cd MauiApp5
dotnet run
```

## 功能测试

### 测试账号配置

| 用户名 | 密码 | 角色 | 所属部门 | 权限说明 |
|--------|------|------|----------|----------|
| admin | admin123 | 管理员 | 整理部 | 所有部门的全部权限 |
| operator | op123 | 操作员 | 整理部 | 只能报修整理部设备，可发送给维修部 |
| viewer | view123 | 访客 | 维修部 | 无操作权限，但是维修人员 |

### 测试场景

#### 场景1：管理员权限测试
1. **登录**：使用 `admin/admin123`
2. **权限验证**：
   - 访问 `/test-user-department` 查看权限摘要
   - 应显示所有部门的全部权限
   - 显示为高级维修人员
3. **报修功能**：
   - 访问 `/create-repair-order`
   - 可以选择所有部门的设备
   - 可以选择所有维修部门
   - 可以指定维修人员或自动分配
4. **管理功能**：
   - 访问 `/role-department-permission-management`
   - 查看权限配置矩阵
   - 访问 `/maintenance-personnel-management`
   - 查看维修人员列表和统计

#### 场景2：操作员权限测试
1. **登录**：使用 `operator/op123`
2. **权限验证**：
   - 访问 `/test-user-department`
   - 应只显示整理部的报修权限和维修部的接收权限
   - 不是维修人员
3. **报修功能**：
   - 访问 `/create-repair-order`
   - 只能选择整理部的设备
   - 可以选择维修部作为维修部门
   - 可以看到维修人员列表和智能推荐

#### 场景3：访客权限测试
1. **登录**：使用 `viewer/view123`
2. **权限验证**：
   - 访问 `/test-user-department`
   - 应显示无操作权限
   - 显示为中级维修人员
3. **报修功能**：
   - 访问 `/create-repair-order`
   - 应显示权限不足的提示
   - 无法创建报修单

#### 场景4：智能分配测试
1. **使用管理员账号**创建报修单
2. **选择不同设备类型**，观察维修人员推荐
3. **测试自动分配**：不指定维修人员直接提交
4. **验证分配结果**：查看分配的维修人员是否合适

### 权限边界测试

#### 测试1：跨部门权限验证
- 使用操作员账号尝试报修其他部门设备
- 系统应阻止并显示错误信息

#### 测试2：技能匹配验证
- 查看不同设备型号的技能要求
- 验证维修人员推荐是否符合技能要求

#### 测试3：工作负载平衡
- 创建多个报修单
- 观察系统是否平衡分配给不同维修人员

## 性能测试

### 测试指标
- 权限查询响应时间 < 100ms
- 设备列表加载时间 < 500ms
- 维修人员查询时间 < 200ms
- 智能分配算法执行时间 < 300ms

### 测试方法
```javascript
// 在浏览器控制台执行性能测试
console.time('权限查询');
// 执行权限相关操作
console.timeEnd('权限查询');
```

## 故障排除

### 常见问题

#### 1. 权限数据未生效
**症状**：用户看不到预期的权限
**解决方案**：
```sql
-- 检查权限数据
SELECT * FROM RoleDepartmentPermissions WHERE IsEnabled = 1
SELECT * FROM UserRoles WHERE UserId = [用户ID]
```

#### 2. 维修人员不显示
**症状**：维修人员列表为空
**解决方案**：
```sql
-- 检查维修人员数据
SELECT * FROM MaintenancePersonnel WHERE IsEnabled = 1
-- 检查部门关联
SELECT mp.*, u.DisplayName, d.Name as DepartmentName 
FROM MaintenancePersonnel mp
LEFT JOIN Users u ON mp.UserId = u.Id
LEFT JOIN Departments d ON mp.DepartmentId = d.Id
```

#### 3. 智能分配失败
**症状**：自动分配维修人员失败
**解决方案**：
```sql
-- 检查设备技能要求
SELECT * FROM EquipmentModelSkills WHERE ModelId = [设备型号ID]
-- 检查维修人员技能
SELECT * FROM MaintenancePersonnel WHERE Specialties IS NOT NULL
```

### 日志调试
启用详细日志记录：
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "MauiApp5.Shared.Services": "Debug"
    }
  }
}
```

## 安全检查清单

### 权限安全
- [ ] 所有权限检查在后端进行验证
- [ ] 前端权限控制仅用于用户体验优化
- [ ] 敏感数据不会泄露给无权限用户
- [ ] 权限变更有适当的审计记录

### 数据安全
- [ ] 数据库连接字符串安全存储
- [ ] SQL注入防护已启用
- [ ] 用户输入已进行验证和清理
- [ ] 错误信息不泄露敏感信息

### 业务安全
- [ ] 用户只能操作授权范围内的数据
- [ ] 跨部门操作被正确阻止
- [ ] 维修人员分配符合技能要求
- [ ] 工作负载限制被正确执行

## 监控和维护

### 关键指标监控
1. **权限查询频率**：监控权限服务调用频率
2. **失败率**：监控权限验证失败率
3. **响应时间**：监控各服务响应时间
4. **用户活动**：监控用户操作模式

### 定期维护任务
1. **权限审计**：定期审查用户权限配置
2. **数据清理**：清理过期的报修单和日志
3. **性能优化**：优化慢查询和索引
4. **安全更新**：及时应用安全补丁

## 扩展计划

### 短期扩展
1. **权限管理界面完善**：添加创建和编辑对话框
2. **报表功能**：权限使用统计和分析
3. **通知系统**：权限变更通知

### 长期扩展
1. **工作流引擎**：复杂的审批流程
2. **移动端优化**：MAUI应用功能增强
3. **API接口**：对外提供权限查询API
4. **多租户支持**：支持多个组织的权限隔离
