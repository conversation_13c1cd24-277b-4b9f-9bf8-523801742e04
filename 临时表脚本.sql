-- 创建临时表存储化学品库存数据
CREATE TABLE #ChemicalInventory (
    代号 NVARCHAR(20) NOT NULL,
    名称 NVARCHAR(100) NOT NULL,
    库存量 INT NOT NULL,
    批号 NVARCHAR(50) NOT NULL,
    入库时间 DATE NOT NULL
);

-- 插入数据
INSERT INTO #ChemicalInventory (代号, 名称, 库存量, 批号, 入库时间) VALUES
('CS000012', '雅格素钻石蓝S-R', 2000, '01D5D0103-N0', '2025-3-20'),
('CS000012', '雅格素钻石蓝S-R', 500, '01134S0424-LI', '2024-7-14'),
('CS000018', '雷马素藏青GC', 1500, 'RC10324', '2024-6-28'),
('CS000018', '雷马素藏青GC', 1000, 'UC10212', '2024-7-26'),
('CS000018', '雷马素藏青GC', 1300, 'UC10211', '2024-7-26'),
('CS000025', '湖北华丽一活性红3BS', 600, '32411009', '2024-12-7'),
('CS000025', '湖北华丽一活性红3BS', 3600, '32412040', '2025-4-13'),
('CS000029', '湖北华丽一活性橙HW-3R', 2375, '20240525', '2024-6-28'),
('CS000029', '湖北华丽一活性橙HW-3R', 600, '202404029', '2024-4-24'),
('CS000564', 'AVITERA LIGHT BLUE SE', 575, '5504006964', '2025-6-25'),
('CS000564', 'AVITERA LIGHT BLUE SE', 425, '5504006965', '2025-6-25'),
('CS000610', '艳丽牛ERIOFAST BLACK M', 2100, '66813400', '2020-12-19'),
('CS000610', '艳丽牛ERIOFAST BLACK M', 1500, '66813500', '2020-12-19'),
('CS000790', 'NOVACRON SUPER BLACK G', 720, 'CS00101Y25', '2025-2-21'),
('CS000790', 'NOVACRON SUPER BLACK G', 510, 'CS24101122', '2025-3-18');

-- 查看临时表数据
SELECT * FROM #ChemicalInventory 
ORDER BY 代号, 入库时间;

-- 按化学品代号统计总库存量
SELECT 
    代号,
    名称,
    SUM(库存量) AS 总库存量,
    COUNT(*) AS 批次数量,
    MIN(入库时间) AS 最早入库时间,
    MAX(入库时间) AS 最新入库时间
FROM #ChemicalInventory
GROUP BY 代号, 名称
ORDER BY 代号;

-- 查看即将过期的库存（假设入库超过1年需要关注）
SELECT 
    代号,
    名称,
    库存量,
    批号,
    入库时间,
    DATEDIFF(DAY, 入库时间, GETDATE()) AS 入库天数
FROM #ChemicalInventory
WHERE DATEDIFF(DAY, 入库时间, GETDATE()) > 365
ORDER BY 入库时间;

-- 使用完毕后删除临时表
-- DROP TABLE #ChemicalInventory; 