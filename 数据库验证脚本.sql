-- =============================================
-- 数据库验证脚本
-- 用于验证数据插入是否成功
-- =============================================

USE [EnterpriseManagementSystem]
GO

PRINT '==============================================';
PRINT '开始验证数据库数据完整性...';
PRINT '==============================================';

-- 1. 验证部门类型数据
PRINT '1. 验证部门类型数据...';
SELECT COUNT(*) as DepartmentTypeCount FROM DepartmentTypes;
SELECT Code, Name FROM DepartmentTypes ORDER BY SortOrder;

-- 2. 验证工种类型数据
PRINT '2. 验证工种类型数据...';
SELECT COUNT(*) as JobTypeCount FROM JobTypes;
SELECT Category, COUNT(*) as Count FROM JobTypes GROUP BY Category ORDER BY Category;

-- 3. 验证部门数据
PRINT '3. 验证部门数据...';
SELECT COUNT(*) as DepartmentCount FROM Departments;
SELECT d.Code, d.Name, dt.Name as DepartmentType 
FROM Departments d 
LEFT JOIN DepartmentTypes dt ON d.DepartmentTypeId = dt.Id 
ORDER BY d.SortOrder;

-- 4. 验证用户数据
PRINT '4. 验证用户数据...';
SELECT COUNT(*) as UserCount FROM Users;
SELECT u.Username, u.DisplayName, d.Name as Department, jt.Name as JobType
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.Id
LEFT JOIN JobTypes jt ON u.JobTypeId = jt.Id
ORDER BY u.Username;

-- 5. 验证设备型号数据
PRINT '5. 验证设备型号数据...';
SELECT COUNT(*) as EquipmentModelCount FROM EquipmentModels;

-- 检查可能缺失的设备型号
DECLARE @MissingModels TABLE (Code NVARCHAR(50));
INSERT INTO @MissingModels VALUES 
('DXJ'), ('SMJ'), ('TJJ'), ('SGJ'), ('QLJ'), ('TSJ'), ('SXJ'), ('PHJ'), ('ZHJ'), ('QMJ'),
('ZGJ'), ('YSJ'), ('MMJ'), ('GZJ'), ('XYJ'), ('KFJ'), ('CCJ'), ('JMJ'), ('ZNJ'), ('TJJ2'),
('FCJ'), ('DDJ'), ('ZJJ'), ('HXJ'), ('XZC'), ('KSQ');

SELECT mm.Code as MissingModelCode
FROM @MissingModels mm
LEFT JOIN EquipmentModels em ON mm.Code = em.Code
WHERE em.Code IS NULL;

-- 6. 验证位置数据
PRINT '6. 验证位置数据...';
SELECT COUNT(*) as LocationCount FROM Locations;
SELECT Code, Name, DepartmentId FROM Locations ORDER BY Code;

-- 7. 验证设备数据
PRINT '7. 验证设备数据...';
SELECT COUNT(*) as EquipmentCount FROM Equipment;

-- 检查设备数据中的NULL值
SELECT 
    COUNT(*) as TotalEquipment,
    SUM(CASE WHEN DepartmentId IS NULL THEN 1 ELSE 0 END) as NullDepartmentId,
    SUM(CASE WHEN ModelId IS NULL THEN 1 ELSE 0 END) as NullModelId,
    SUM(CASE WHEN LocationId IS NULL THEN 1 ELSE 0 END) as NullLocationId
FROM Equipment;

-- 8. 验证维修人员数据
PRINT '8. 验证维修人员数据...';
SELECT COUNT(*) as MaintenancePersonnelCount FROM MaintenancePersonnel;
SELECT mp.*, u.DisplayName, d.Name as Department
FROM MaintenancePersonnel mp
LEFT JOIN Users u ON mp.UserId = u.Id
LEFT JOIN Departments d ON mp.DepartmentId = d.Id;

-- 9. 验证角色部门权限数据
PRINT '9. 验证角色部门权限数据...';
SELECT COUNT(*) as RoleDepartmentPermissionCount FROM RoleDepartmentPermissions;

-- 10. 验证角色部门权限数据
PRINT '10. 验证角色部门权限数据...';
SELECT COUNT(*) as RoleDepartmentPermissionCount FROM RoleDepartmentPermissions;

PRINT '==============================================';
PRINT '数据验证完成！';
PRINT '==============================================';

-- 最终检查：查找可能的数据完整性问题
PRINT '最终检查：数据完整性问题...';

-- 检查设备表中的外键完整性
SELECT 'Equipment with NULL DepartmentId' as Issue, COUNT(*) as Count
FROM Equipment WHERE DepartmentId IS NULL
UNION ALL
SELECT 'Equipment with NULL ModelId' as Issue, COUNT(*) as Count
FROM Equipment WHERE ModelId IS NULL
UNION ALL
SELECT 'Equipment with NULL LocationId' as Issue, COUNT(*) as Count
FROM Equipment WHERE LocationId IS NULL
UNION ALL
SELECT 'Equipment with invalid DepartmentId' as Issue, COUNT(*) as Count
FROM Equipment e LEFT JOIN Departments d ON e.DepartmentId = d.Id WHERE d.Id IS NULL
UNION ALL
SELECT 'Equipment with invalid ModelId' as Issue, COUNT(*) as Count
FROM Equipment e LEFT JOIN EquipmentModels em ON e.ModelId = em.Id WHERE em.Id IS NULL
UNION ALL
SELECT 'Equipment with invalid LocationId' as Issue, COUNT(*) as Count
FROM Equipment e LEFT JOIN Locations l ON e.LocationId = l.Id WHERE l.Id IS NULL;

PRINT '如果上述查询结果都显示 Count = 0，则数据完整性良好！';
