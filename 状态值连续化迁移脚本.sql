-- =============================================
-- 报修单状态值连续化迁移脚本
-- 将"待确认"状态从8改为6，移除"已暂停"状态
-- =============================================

USE [EnterpriseManagementSystem]
GO

PRINT '开始报修单状态值连续化迁移...'
PRINT '========================================'

-- 检查当前状态分布
PRINT '迁移前状态分布：'
SELECT 
    Status,
    CASE Status
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'已暂停（将被移除）'
        WHEN 8 THEN N'待确认（将改为6）'
        ELSE N'未知状态'
    END AS StatusName,
    COUNT(*) as Count
FROM RepairOrders
GROUP BY Status
ORDER BY Status

PRINT ''
PRINT '开始迁移操作...'

-- 开始事务
BEGIN TRANSACTION

BEGIN TRY
    -- 1. 检查是否有状态为6（已暂停）的记录
    DECLARE @pausedCount INT
    SELECT @pausedCount = COUNT(*) FROM RepairOrders WHERE Status = 6
    
    IF @pausedCount > 0
    BEGIN
        PRINT '警告：发现 ' + CAST(@pausedCount AS VARCHAR(10)) + ' 个"已暂停"状态的报修单'
        PRINT '这些记录将被更新为"处理中"状态'
        
        -- 将"已暂停"状态改为"处理中"
        UPDATE RepairOrders 
        SET Status = 2, -- 处理中
            UpdatedAt = GETDATE()
        WHERE Status = 6
        
        PRINT '✓ 已将 ' + CAST(@pausedCount AS VARCHAR(10)) + ' 个"已暂停"记录更新为"处理中"'
    END
    ELSE
    BEGIN
        PRINT '✓ 没有发现"已暂停"状态的记录'
    END
    
    -- 2. 将状态8（待确认）改为状态6
    DECLARE @pendingConfirmationCount INT
    SELECT @pendingConfirmationCount = COUNT(*) FROM RepairOrders WHERE Status = 8
    
    IF @pendingConfirmationCount > 0
    BEGIN
        PRINT '发现 ' + CAST(@pendingConfirmationCount AS VARCHAR(10)) + ' 个"待确认"状态的报修单'
        PRINT '将状态值从8更新为6...'
        
        UPDATE RepairOrders 
        SET Status = 6, -- 新的待确认状态值
            UpdatedAt = GETDATE()
        WHERE Status = 8
        
        PRINT '✓ 已将 ' + CAST(@pendingConfirmationCount AS VARCHAR(10)) + ' 个"待确认"记录的状态值更新为6'
    END
    ELSE
    BEGIN
        PRINT '✓ 没有发现状态值为8的"待确认"记录'
    END
    
    -- 3. 更新工作流历史表中的状态值
    PRINT ''
    PRINT '更新工作流历史表...'
    
    -- 更新FromStatus
    DECLARE @historyFromCount INT
    SELECT @historyFromCount = COUNT(*) FROM RepairWorkflowHistory WHERE FromStatus = 8
    
    IF @historyFromCount > 0
    BEGIN
        UPDATE RepairWorkflowHistory 
        SET FromStatus = 6
        WHERE FromStatus = 8
        
        PRINT '✓ 已更新 ' + CAST(@historyFromCount AS VARCHAR(10)) + ' 条工作流历史记录的FromStatus'
    END
    
    -- 更新ToStatus
    DECLARE @historyToCount INT
    SELECT @historyToCount = COUNT(*) FROM RepairWorkflowHistory WHERE ToStatus = 8
    
    IF @historyToCount > 0
    BEGIN
        UPDATE RepairWorkflowHistory 
        SET ToStatus = 6
        WHERE ToStatus = 8
        
        PRINT '✓ 已更新 ' + CAST(@historyToCount AS VARCHAR(10)) + ' 条工作流历史记录的ToStatus'
    END
    
    -- 处理FromStatus = 6的历史记录（原来的已暂停状态）
    DECLARE @historyPausedFromCount INT
    SELECT @historyPausedFromCount = COUNT(*) FROM RepairWorkflowHistory WHERE FromStatus = 6 AND ToStatus != 6
    
    IF @historyPausedFromCount > 0
    BEGIN
        -- 将原来的"已暂停"状态历史记录的FromStatus改为2（处理中）
        UPDATE RepairWorkflowHistory 
        SET FromStatus = 2
        WHERE FromStatus = 6 AND ToStatus != 6
        
        PRINT '✓ 已更新 ' + CAST(@historyPausedFromCount AS VARCHAR(10)) + ' 条原"已暂停"状态的历史记录'
    END
    
    -- 处理ToStatus = 6的历史记录（原来的已暂停状态）
    DECLARE @historyPausedToCount INT
    SELECT @historyPausedToCount = COUNT(*) FROM RepairWorkflowHistory WHERE ToStatus = 6 AND FromStatus != 6
    
    IF @historyPausedToCount > 0
    BEGIN
        -- 将原来的"已暂停"状态历史记录的ToStatus改为2（处理中）
        UPDATE RepairWorkflowHistory 
        SET ToStatus = 2
        WHERE ToStatus = 6 AND FromStatus != 6
        
        PRINT '✓ 已更新 ' + CAST(@historyPausedToCount AS VARCHAR(10)) + ' 条原"已暂停"状态的历史记录'
    END
    
    -- 提交事务
    COMMIT TRANSACTION
    
    PRINT ''
    PRINT '✅ 迁移成功完成！'
    
END TRY
BEGIN CATCH
    -- 回滚事务
    ROLLBACK TRANSACTION
    
    PRINT ''
    PRINT '❌ 迁移失败，已回滚所有更改'
    PRINT '错误信息：' + ERROR_MESSAGE()
    
    -- 重新抛出错误
    THROW
END CATCH

PRINT ''
PRINT '迁移后状态分布：'
SELECT 
    Status,
    CASE Status
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'待确认'
        ELSE N'未知状态'
    END AS StatusName,
    COUNT(*) as Count
FROM RepairOrders
GROUP BY Status
ORDER BY Status

PRINT ''
PRINT '========================================'
PRINT '状态值连续化迁移完成！'
PRINT ''
PRINT '新的状态定义：'
PRINT '1 = 待处理'
PRINT '2 = 处理中'
PRINT '3 = 已完成'
PRINT '4 = 已作废'
PRINT '5 = 已关闭'
PRINT '6 = 待确认'
PRINT ''
PRINT '注意：状态值现在是连续的，没有跳跃'
PRINT '========================================'
