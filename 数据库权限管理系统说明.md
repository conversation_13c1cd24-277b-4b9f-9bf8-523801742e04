# 基于SQL Server的完整权限管理系统

## 系统概述

本系统是一个基于 .NET 8 Blazor、SQL Server 数据库和 SqlSugar ORM 的完整权限管理系统，采用 RBAC（基于角色的访问控制）模型，提供了高安全性、高性能的用户权限管理解决方案。

## 技术栈

- **前端框架**: .NET 8 Blazor Server & MAUI Hybrid
- **数据库**: SQL Server (支持 LocalDB 和完整版)
- **ORM框架**: SqlSugar 5.1.4
- **密码加密**: BCrypt.Net-Next
- **UI组件**: AntDesign Blazor
- **认证授权**: .NET Core Authentication & Authorization

## 系统架构

### 1. 数据库设计

#### 核心表结构

```sql
-- 用户表
Users (Id, Username, PasswordHash, DisplayName, Email, Phone, IsEnabled, IsLocked, ...)

-- 角色表  
Roles (Id, Code, Name, Description, SortOrder, IsEnabled, IsSystem, ...)

-- 权限表
Permissions (Id, Code, Name, Module, Action, Level, RouteUrl, ...)

-- 用户角色关联表
UserRoles (Id, UserId, RoleId, AssignedAt, ExpiresAt, IsEnabled, ...)

-- 角色权限关联表
RolePermissions (Id, RoleId, PermissionId, AssignedAt, IsEnabled, ...)

-- 用户直接权限表
UserPermissions (Id, UserId, PermissionId, IsGranted, ExpiresAt, ...)

-- 登录日志表
LoginLogs (Id, Username, ClientIP, IsSuccess, FailureReason, LoginTime)
```

#### 权限设计理念

- **层级权限**: 支持菜单级、页面级、功能级、数据级四个层次
- **灵活分配**: 支持角色权限 + 用户直接权限的组合模式
- **权限继承**: 用户通过角色继承权限，同时支持直接分配
- **权限拒绝**: 支持显式拒绝权限，覆盖角色继承的权限

### 2. 服务层设计

#### 核心服务接口

```csharp
// 用户认证服务
IUserAuthenticationService
├── ValidateUserAsync()      // 用户登录验证
├── GetUserInfo()           // 获取用户信息
└── GetAllUsers()           // 获取所有用户

// 数据库上下文
DatabaseContext
├── InitializeDatabaseAsync() // 数据库初始化
├── Users, Roles, Permissions // 实体集合
└── CreateTables, SeedData    // 表创建和数据初始化

// 用户管理服务
IUserManagementService
├── 用户管理 (CRUD, 状态管理)
├── 角色管理 (分配、移除)
├── 权限管理 (授权、拒绝)
└── 日志查询 (登录日志、审计)
```

#### 双重认证实现

系统提供两种认证方式，可通过配置文件切换：

1. **SqlSugar ORM 方式** (`DatabaseAuthenticationService`)
   - 使用 SqlSugar 的强类型查询
   - 支持复杂的关联查询和权限计算
   - 适合复杂业务场景

2. **存储过程方式** (`StoredProcedureAuthenticationService`)
   - 使用 SQL Server 存储过程验证
   - 高性能，数据库端处理逻辑
   - 适合高并发场景

### 3. 安全特性

#### 密码安全
- **BCrypt 加密**: 使用业界标准的 BCrypt 哈希算法
- **盐值自动生成**: 每次加密自动生成唯一盐值
- **防暴力破解**: 内置防暴力破解机制

#### 账户安全
- **自动锁定**: 连续登录失败 5 次自动锁定账户
- **登录日志**: 记录所有登录尝试，包括 IP 地址
- **状态管理**: 支持账户启用/禁用、锁定/解锁

#### 权限安全
- **最小权限原则**: 默认拒绝，显式授权
- **权限组合**: 角色权限 + 直接权限 - 拒绝权限
- **权限过期**: 支持权限临时授权和自动过期

## 配置说明

### 数据库连接配置

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=MauiPermissionDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  },
  "AuthenticationSettings": {
    "UseStoredProcedure": true,
    "MaxLoginFailureCount": 5,
    "AccountLockoutDuration": 30,
    "PasswordComplexity": {
      "MinLength": 6,
      "RequireDigit": false,
      "RequireLowercase": false,
      "RequireUppercase": false,
      "RequireNonAlphanumeric": false
    }
  }
}
```

### 认证方式切换

通过 `AuthenticationSettings:UseStoredProcedure` 配置项切换：
- `true`: 使用存储过程验证
- `false`: 使用 SqlSugar ORM 验证

## 存储过程详解

### 核心存储过程

#### sp_ValidateUser - 用户验证
```sql
-- 功能：验证用户登录，包含完整的安全检查
-- 参数：用户名、密码、客户端IP
-- 输出：验证结果、用户信息、错误信息
-- 安全特性：
--   - 密码验证（支持 BCrypt）
--   - 账户状态检查
--   - 登录失败计数
--   - 自动锁定机制
--   - 登录日志记录
```

#### sp_GetUserPermissions - 权限获取
```sql
-- 功能：获取用户的完整权限列表
-- 逻辑：角色权限 + 直接授权权限 - 直接拒绝权限
-- 性能：优化的联表查询，支持权限缓存
```

#### sp_CreateUser - 用户创建
```sql
-- 功能：创建新用户
-- 验证：用户名唯一性、邮箱唯一性
-- 安全：密码哈希存储、创建日志
```

## 权限控制组件

### 页面级权限控制

```razor
<PermissionView RequiredPermission="UserManagement.View">
    <ChildContent>
        <!-- 有权限时显示的内容 -->
    </ChildContent>
    <NotAuthorized>
        <!-- 无权限时显示的内容 -->
    </NotAuthorized>
</PermissionView>
```

### 功能级权限控制

```razor
<AuthorizeView>
    <Authorized>
        @if (HasPermission(context.User, "UserManagement.Create"))
        {
            <Button>新增用户</Button>
        }
    </Authorized>
</AuthorizeView>
```

## 测试账号

系统预置了三个测试账号，用于演示不同权限级别：

### 1. 系统管理员
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: Administrator
- **权限**: 全部系统功能
- **可访问**: 所有页面和功能

### 2. 设备操作员
- **用户名**: `operator` 
- **密码**: `op123`
- **角色**: Operator
- **权限**: 设备相关功能
- **可访问**: 首页、设备扫描、设备报修、摄像头测试

### 3. 访客用户
- **用户名**: `viewer`
- **密码**: `view123` 
- **角色**: Viewer
- **权限**: 基础查看功能
- **可访问**: 首页、天气预报

## 数据库初始化

系统支持自动数据库初始化，包括：

1. **表结构创建**: 自动创建所有必需的表
2. **索引创建**: 创建性能优化索引
3. **基础数据**: 初始化权限、角色、用户数据
4. **关联关系**: 建立用户角色和权限关联

### 初始化流程

```csharp
// 程序启动时自动执行
await dbContext.InitializeDatabaseAsync();

// 包含以下步骤：
1. CreateTablesAsync()      // 创建表结构
2. CreateIndexesAsync()     // 创建索引
3. SeedPermissionsAsync()   // 初始化权限
4. SeedRolesAsync()         // 初始化角色
5. SeedUsersAsync()         // 初始化用户
6. SeedRolePermissionsAsync() // 分配角色权限
7. SeedUserRolesAsync()     // 分配用户角色
```

## 性能优化

### 数据库层面
- **索引优化**: 为关键查询字段创建复合索引
- **查询优化**: 使用存储过程减少网络往返
- **连接池**: SqlSugar 自动管理数据库连接池

### 应用层面
- **权限缓存**: 用户权限信息缓存到客户端
- **懒加载**: 权限数据按需加载
- **批量操作**: 支持批量用户和权限管理

### 索引策略

```sql
-- 用户表索引
IX_Users_Username_IsEnabled (Username, IsEnabled)
IX_Users_Email

-- 权限相关索引  
IX_UserRoles_UserId_IsEnabled (UserId, IsEnabled)
IX_RolePermissions_RoleId_IsEnabled (RoleId, IsEnabled)
IX_UserPermissions_UserId_IsEnabled (UserId, IsEnabled)
IX_Permissions_Code_IsEnabled (Code, IsEnabled)

-- 日志表索引
IX_LoginLogs_Username_LoginTime (Username, LoginTime)
IX_LoginLogs_IsSuccess_LoginTime (IsSuccess, LoginTime)
```

## 扩展功能

### 已实现功能
- ✅ 用户管理 (增删改查、状态管理)
- ✅ 角色权限管理
- ✅ 登录日志查询
- ✅ 自动账户锁定
- ✅ 密码安全加密
- ✅ 权限组件化控制

### 建议扩展功能
- 🔄 用户权限审计日志
- 🔄 权限申请和审批流程
- 🔄 单点登录 (SSO) 集成
- 🔄 二次认证 (2FA)
- 🔄 IP 白名单限制
- 🔄 会话管理和在线用户
- 🔄 权限报表和统计
- 🔄 数据权限控制

## 部署指南

### 开发环境
1. 确保安装 .NET 8 SDK
2. 安装 SQL Server LocalDB 或完整版
3. 克隆项目代码
4. 修改连接字符串
5. 运行项目，系统会自动初始化数据库

### 生产环境
1. 部署到 IIS 或 Docker
2. 配置 SQL Server 连接字符串
3. 设置适当的密码复杂度要求
4. 启用 HTTPS
5. 配置日志记录和监控

### Docker 部署示例

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY published/ .
EXPOSE 80
ENTRYPOINT ["dotnet", "MauiApp5.Web.dll"]
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查连接字符串是否正确
   - 确认 SQL Server 服务是否启动
   - 验证数据库权限设置

2. **权限验证不生效**
   - 检查权限配置是否正确
   - 验证用户角色分配
   - 确认权限组件使用正确

3. **登录失败**
   - 检查用户账户状态
   - 验证密码是否正确
   - 查看登录日志确定失败原因

4. **性能问题**
   - 检查数据库索引是否创建
   - 监控 SQL 查询性能
   - 考虑启用权限缓存

## 安全建议

### 生产环境安全配置

1. **密码策略**
   ```json
   "PasswordComplexity": {
     "MinLength": 8,
     "RequireDigit": true,
     "RequireLowercase": true,
     "RequireUppercase": true,
     "RequireNonAlphanumeric": true
   }
   ```

2. **账户锁定策略**
   - 降低失败次数阈值 (3-5次)
   - 设置锁定持续时间
   - 启用管理员解锁功能

3. **网络安全**
   - 启用 HTTPS
   - 配置防火墙
   - 使用 IP 白名单

4. **数据库安全**
   - 使用专用数据库账户
   - 启用透明数据加密 (TDE)
   - 定期备份数据

## 总结

本权限管理系统提供了企业级的安全性和可扩展性，采用现代化的技术栈和最佳实践，适用于各种规模的应用系统。通过灵活的权限模型和丰富的管理功能，可以满足复杂的业务权限需求。

系统的设计理念是安全第一、性能优化、易于维护，为开发者提供了完整的权限管理解决方案。 